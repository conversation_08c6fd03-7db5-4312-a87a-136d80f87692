// Comprehensive error handling types and utilities

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  DATABASE = 'database',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system',
  USER_INPUT = 'user_input'
}

export interface ErrorContext {
  userId?: string;
  action?: string;
  component?: string;
  timestamp: string;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

export interface AppError {
  id: string;
  code: string;
  message: string;
  userMessage: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context: ErrorContext;
  originalError?: Error;
  retryable: boolean;
  recoverable: boolean;
}

export interface ErrorState {
  hasError: boolean;
  error: AppError | null;
  retryCount: number;
  lastRetryAt: string | null;
  isRetrying: boolean;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: ErrorCategory[];
}

export interface FallbackData<T> {
  data: T;
  isStale: boolean;
  lastUpdated: string;
}

// Error recovery strategies
export enum RecoveryStrategy {
  RETRY = 'retry',
  FALLBACK = 'fallback',
  GRACEFUL_DEGRADATION = 'graceful_degradation',
  USER_ACTION_REQUIRED = 'user_action_required',
  RELOAD_REQUIRED = 'reload_required'
}

export interface RecoveryAction {
  strategy: RecoveryStrategy;
  label: string;
  action: () => void | Promise<void>;
  autoExecute?: boolean;
  delay?: number;
}

// User-friendly error messages
export const ERROR_MESSAGES = {
  [ErrorCategory.NETWORK]: {
    default: 'Connection problem. Please check your internet connection and try again.',
    timeout: 'The request is taking longer than expected. Please try again.',
    offline: 'You appear to be offline. Please check your connection.'
  },
  [ErrorCategory.AUTHENTICATION]: {
    default: 'Please sign in to continue.',
    expired: 'Your session has expired. Please sign in again.',
    invalid: 'Invalid credentials. Please check your email and password.'
  },
  [ErrorCategory.AUTHORIZATION]: {
    default: 'You don\'t have permission to perform this action.',
    insufficient: 'Insufficient permissions for this operation.'
  },
  [ErrorCategory.VALIDATION]: {
    default: 'Please check your input and try again.',
    required: 'This field is required.',
    format: 'Please enter a valid value.'
  },
  [ErrorCategory.DATABASE]: {
    default: 'Unable to save changes. Please try again.',
    conflict: 'This record has been modified by another user. Please refresh and try again.',
    notFound: 'The requested information could not be found.'
  },
  [ErrorCategory.BUSINESS_LOGIC]: {
    default: 'Unable to complete this action. Please try again or contact support.',
    constraint: 'This action violates business rules.'
  },
  [ErrorCategory.SYSTEM]: {
    default: 'A system error occurred. Please try again or contact support if the problem persists.',
    maintenance: 'The system is currently under maintenance. Please try again later.'
  },
  [ErrorCategory.USER_INPUT]: {
    default: 'Please check your input and try again.',
    invalid: 'The provided information is not valid.'
  }
} as const;

// Default retry configuration
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
  retryableErrors: [
    ErrorCategory.NETWORK,
    ErrorCategory.DATABASE,
    ErrorCategory.SYSTEM
  ]
};
