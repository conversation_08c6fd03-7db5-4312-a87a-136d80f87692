export type CandidateStatus = 
  | 'new' 
  | 'in_progress' 
  | 'on_hold' 
  | 'declined' 
  | 'hired' 
  | 'archived';

export interface Candidate {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  position_id: string;
  location_id: string;
  resume_url: string | null;
  availability: string[];
  rating: number;
  status: CandidateStatus;
  is_new: boolean;
  created_at: string;
  position?: {
    title: string;
  };
  location?: {
    name: string;
  };
}

export interface ApplicationLog {
  id: string;
  candidate_id: string;
  status: CandidateStatus;
  notes: string;
  created_at: string;
  created_by: string;
}
