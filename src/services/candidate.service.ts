import { supabase } from "../lib/supabase";
import { BaseService } from "./base.service";
import type { Candidate, CandidateStatus, ApplicationLog } from "../types";

export class CandidateService extends BaseService {
  private fallbackCandidates: Candidate[] = [];

  async fetchCandidates(): Promise<Candidate[]> {
    return this.executeWithRetry(
      async () => {
        const { data, error } = await supabase
          .from("candidates")
          .select(
            `
            *,
            position:positions(title),
            location:locations(name)
          `
          )
          .order("created_at", { ascending: false });

        if (error) throw error;

        // Cache successful response for fallback
        const candidates = data || [];
        this.fallbackCandidates = candidates;
        return candidates;
      },
      { component: "CandidateService", action: "fetchCandidates" }
    ).catch((error) => {
      // Graceful degradation: return cached data if available
      if (this.fallbackCandidates.length > 0) {
        console.warn(
          "Using cached candidate data due to error:",
          error.message
        );
        return this.fallbackCandidates;
      }

      // Return empty array as last resort
      return this.createFallbackResponse<Candidate[]>([]);
    });
  }

  async updateCandidateStatus(
    candidateId: string,
    status: CandidateStatus
  ): Promise<void> {
    if (!candidateId || !status) {
      throw new Error("Candidate ID and status are required");
    }

    return this.executeWithRetry(
      async () => {
        const { error } = await supabase
          .from("candidates")
          .update({ status })
          .eq("id", candidateId);

        if (error) throw error;
      },
      { component: "CandidateService", action: "updateCandidateStatus" }
    );
  }

  async updateCandidateRating(
    candidateId: string,
    rating: number
  ): Promise<void> {
    if (!candidateId || rating < 1 || rating > 5) {
      throw new Error("Valid candidate ID and rating (1-5) are required");
    }

    return this.executeWithRetry(
      async () => {
        const { error } = await supabase
          .from("candidates")
          .update({ rating })
          .eq("id", candidateId);

        if (error) throw error;
      },
      { component: "CandidateService", action: "updateCandidateRating" }
    );
  }

  async fetchApplicationLogs(candidateId: string): Promise<ApplicationLog[]> {
    try {
      const { data, error } = await supabase
        .from("application_logs")
        .select("*")
        .eq("candidate_id", candidateId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      this.handleError(error);
    }
  }

  async addApplicationLog(
    candidateId: string,
    status: CandidateStatus,
    notes: string
  ): Promise<void> {
    try {
      const { error } = await supabase.from("application_logs").insert({
        candidate_id: candidateId,
        status,
        notes,
        created_at: new Date().toISOString(),
      });

      if (error) throw error;
    } catch (error) {
      this.handleError(error);
    }
  }
}

export const candidateService = new CandidateService();
