import { supabase } from '../lib/supabase';
import { BaseService } from './base.service';
import type { Candidate, CandidateStatus, ApplicationLog } from '../types';

export class CandidateService extends BaseService {
  async fetchCandidates(): Promise<Candidate[]> {
    try {
      const { data, error } = await supabase
        .from('candidates')
        .select(`
          *,
          position:positions(title),
          location:locations(name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateCandidateStatus(candidateId: string, status: CandidateStatus): Promise<void> {
    try {
      const { error } = await supabase
        .from('candidates')
        .update({ status })
        .eq('id', candidateId);

      if (error) throw error;
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateCandidateRating(candidateId: string, rating: number): Promise<void> {
    try {
      const { error } = await supabase
        .from('candidates')
        .update({ rating })
        .eq('id', candidateId);

      if (error) throw error;
    } catch (error) {
      this.handleError(error);
    }
  }

  async fetchApplicationLogs(candidateId: string): Promise<ApplicationLog[]> {
    try {
      const { data, error } = await supabase
        .from('application_logs')
        .select('*')
        .eq('candidate_id', candidateId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      this.handleError(error);
    }
  }

  async addApplicationLog(candidateId: string, status: CandidateStatus, notes: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('application_logs')
        .insert({
          candidate_id: candidateId,
          status,
          notes,
          created_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      this.handleError(error);
    }
  }
}

export const candidateService = new CandidateService();
