import { supabase } from "../lib/supabase";
import { createAppError, logError } from "../utils/errorHandler";
import { withRetry, withTimeout } from "../utils/retryHandler";

export abstract class BaseService {
  protected handleError(
    error: any,
    context?: { component?: string; action?: string }
  ): never {
    const appError = createAppError(
      error instanceof Error ? error : new Error(String(error)),
      context
    );

    logError(appError);
    throw appError;
  }

  protected async getCurrentUser() {
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();
      if (error) throw error;
      if (!user) throw new Error("No authenticated user found");
      return user;
    } catch (error) {
      this.handleError(error, { action: "getCurrentUser" });
    }
  }

  // Enhanced database operation with retry and timeout
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    context?: { component?: string; action?: string }
  ): Promise<T> {
    try {
      return await withTimeout(
        () => withRetry(operation, {}, context),
        30000 // 30 second timeout
      );
    } catch (error) {
      this.handleError(error, context);
    }
  }

  // Graceful degradation helper
  protected createFallbackResponse<T>(defaultData: T): T {
    return defaultData;
  }

  // Network connectivity check
  protected async checkConnectivity(): Promise<boolean> {
    try {
      const { error } = await supabase.from("locations").select("id").limit(1);
      return !error;
    } catch {
      return false;
    }
  }
}
