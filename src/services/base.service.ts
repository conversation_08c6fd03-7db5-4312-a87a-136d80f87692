import { supabase } from '../lib/supabase';

export abstract class BaseService {
  protected handleError(error: any): never {
    console.error('Service error:', error);
    throw new Error(error.message || 'An unexpected error occurred');
  }

  protected async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    if (!user) throw new Error('No authenticated user found');
    return user;
  }
}
