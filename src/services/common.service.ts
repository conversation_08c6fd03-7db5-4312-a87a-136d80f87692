import { supabase } from "../lib/supabase";
import { BaseService } from "./base.service";
import type { Position, Location } from "../types";

export class CommonService extends BaseService {
  private fallbackPositions: Position[] = [];
  private fallbackLocations: Location[] = [];

  async fetchPositions(): Promise<Position[]> {
    return this.executeWithRetry(
      async () => {
        const { data, error } = await supabase
          .from("positions")
          .select("*")
          .eq("is_active", true)
          .order("title");

        if (error) throw error;

        // Cache successful response for fallback
        const positions = data || [];
        this.fallbackPositions = positions;
        return positions;
      },
      { component: "CommonService", action: "fetchPositions" }
    ).catch((error) => {
      // Graceful degradation: return cached data if available
      if (this.fallbackPositions.length > 0) {
        console.warn(
          "Using cached positions data due to error:",
          error.message
        );
        return this.fallbackPositions;
      }

      // Return empty array as last resort
      return this.createFallbackResponse<Position[]>([]);
    });
  }

  async fetchLocations(): Promise<Location[]> {
    return this.executeWithRetry(
      async () => {
        const { data, error } = await supabase
          .from("locations")
          .select("*")
          .order("name");

        if (error) throw error;

        // Cache successful response for fallback
        const locations = data || [];
        this.fallbackLocations = locations;
        return locations;
      },
      { component: "CommonService", action: "fetchLocations" }
    ).catch((error) => {
      // Graceful degradation: return cached data if available
      if (this.fallbackLocations.length > 0) {
        console.warn(
          "Using cached locations data due to error:",
          error.message
        );
        return this.fallbackLocations;
      }

      // Return empty array as last resort
      return this.createFallbackResponse<Location[]>([]);
    });
  }
}

export const commonService = new CommonService();
