import { supabase } from '../lib/supabase';
import { BaseService } from './base.service';
import type { Position, Location } from '../types';

export class CommonService extends BaseService {
  async fetchPositions(): Promise<Position[]> {
    try {
      const { data, error } = await supabase
        .from('positions')
        .select('*')
        .eq('is_active', true)
        .order('title');

      if (error) throw error;
      return data || [];
    } catch (error) {
      this.handleError(error);
    }
  }

  async fetchLocations(): Promise<Location[]> {
    try {
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      this.handleError(error);
    }
  }
}

export const commonService = new CommonService();
