import { CandidateService } from './candidate.service';
import { AdminService } from './admin.service';
import { CommonService } from './common.service';

/**
 * Service Container for dependency injection
 * Provides centralized access to all services with better testability
 */
export class ServiceContainer {
  private static instance: ServiceContainer;
  
  public readonly candidate: CandidateService;
  public readonly admin: AdminService;
  public readonly common: CommonService;

  constructor(
    candidateService?: CandidateService,
    adminService?: AdminService,
    commonService?: CommonService
  ) {
    this.candidate = candidateService || new CandidateService();
    this.admin = adminService || new AdminService();
    this.common = commonService || new CommonService();
  }

  /**
   * Get the singleton instance of the service container
   */
  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Set a custom instance (useful for testing)
   */
  static setInstance(instance: ServiceContainer): void {
    ServiceContainer.instance = instance;
  }

  /**
   * Reset the singleton instance (useful for testing)
   */
  static reset(): void {
    ServiceContainer.instance = undefined as any;
  }

  /**
   * Create a test instance with mock services
   */
  static createTestInstance(
    candidateService?: CandidateService,
    adminService?: AdminService,
    commonService?: CommonService
  ): ServiceContainer {
    return new ServiceContainer(candidateService, adminService, commonService);
  }
}

/**
 * Default service container instance
 */
export const services = ServiceContainer.getInstance();

/**
 * Hook for accessing services in React components
 */
export function useServices(): ServiceContainer {
  return ServiceContainer.getInstance();
}
