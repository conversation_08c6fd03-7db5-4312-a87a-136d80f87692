import { supabase } from "../lib/supabase";
import { BaseService } from "./base.service";
import type { AdminProfile, AdminPreferences } from "../types";

export class AdminService extends BaseService {
  private fallbackProfile: AdminProfile | null = null;
  private fallbackPreferences: AdminPreferences | null = null;

  async fetchAdminProfile(): Promise<AdminProfile | null> {
    return this.executeWithRetry(
      async () => {
        const user = await this.getCurrentUser();

        const { data, error } = await supabase
          .from("admin_profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        if (error && error.code !== "PGRST116") throw error;

        // Cache successful response for fallback
        this.fallbackProfile = data || null;
        return data || null;
      },
      { component: "AdminService", action: "fetchAdminProfile" }
    ).catch((error) => {
      // Graceful degradation: return cached data if available
      if (this.fallbackProfile) {
        console.warn("Using cached admin profile due to error:", error.message);
        return this.fallbackProfile;
      }

      // Return null as fallback
      return this.createFallbackResponse<AdminProfile | null>(null);
    });
  }

  async updateAdminProfile(profile: Partial<AdminProfile>): Promise<void> {
    if (!profile || Object.keys(profile).length === 0) {
      throw new Error("Profile data is required");
    }

    return this.executeWithRetry(
      async () => {
        const user = await this.getCurrentUser();

        const { error } = await supabase.from("admin_profiles").upsert({
          id: user.id,
          ...profile,
        });

        if (error) throw error;

        // Update cached profile
        if (this.fallbackProfile) {
          this.fallbackProfile = { ...this.fallbackProfile, ...profile };
        }
      },
      { component: "AdminService", action: "updateAdminProfile" }
    );
  }

  async fetchAdminPreferences(): Promise<AdminPreferences | null> {
    return this.executeWithRetry(
      async () => {
        const user = await this.getCurrentUser();

        const { data, error } = await supabase
          .from("admin_preferences")
          .select("*")
          .eq("admin_id", user.id)
          .single();

        if (error && error.code !== "PGRST116") throw error;

        // Cache successful response for fallback
        this.fallbackPreferences = data || null;
        return data || null;
      },
      { component: "AdminService", action: "fetchAdminPreferences" }
    ).catch((error) => {
      // Graceful degradation: return cached data if available
      if (this.fallbackPreferences) {
        console.warn(
          "Using cached admin preferences due to error:",
          error.message
        );
        return this.fallbackPreferences;
      }

      // Return null as fallback
      return this.createFallbackResponse<AdminPreferences | null>(null);
    });
  }

  async updateAdminPreferences(
    preferences: Partial<AdminPreferences>
  ): Promise<void> {
    if (!preferences || Object.keys(preferences).length === 0) {
      throw new Error("Preferences data is required");
    }

    return this.executeWithRetry(
      async () => {
        const user = await this.getCurrentUser();

        const { error } = await supabase.from("admin_preferences").upsert({
          admin_id: user.id,
          ...preferences,
        });

        if (error) throw error;

        // Update cached preferences
        if (this.fallbackPreferences) {
          this.fallbackPreferences = {
            ...this.fallbackPreferences,
            ...preferences,
          };
        }
      },
      { component: "AdminService", action: "updateAdminPreferences" }
    );
  }
}

export const adminService = new AdminService();
