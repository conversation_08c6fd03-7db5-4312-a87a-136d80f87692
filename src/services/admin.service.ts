import { supabase } from '../lib/supabase';
import { BaseService } from './base.service';
import type { AdminProfile, AdminPreferences } from '../types';

export class AdminService extends BaseService {
  async fetchAdminProfile(): Promise<AdminProfile | null> {
    try {
      const user = await this.getCurrentUser();
      
      const { data, error } = await supabase
        .from('admin_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateAdminProfile(profile: Partial<AdminProfile>): Promise<void> {
    try {
      const user = await this.getCurrentUser();
      
      const { error } = await supabase
        .from('admin_profiles')
        .upsert({
          id: user.id,
          ...profile,
        });

      if (error) throw error;
    } catch (error) {
      this.handleError(error);
    }
  }

  async fetchAdminPreferences(): Promise<AdminPreferences | null> {
    try {
      const user = await this.getCurrentUser();
      
      const { data, error } = await supabase
        .from('admin_preferences')
        .select('*')
        .eq('admin_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateAdminPreferences(preferences: Partial<AdminPreferences>): Promise<void> {
    try {
      const user = await this.getCurrentUser();
      
      const { error } = await supabase
        .from('admin_preferences')
        .upsert({
          admin_id: user.id,
          ...preferences,
        });

      if (error) throw error;
    } catch (error) {
      this.handleError(error);
    }
  }
}

export const adminService = new AdminService();
