import type { CandidateStatus } from '../types';

export const CANDIDATE_STATUS_OPTIONS: Array<{
  value: CandidateStatus;
  label: string;
}> = [
  { value: 'new', label: 'New' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'on_hold', label: 'On Hold' },
  { value: 'hired', label: 'Hired' },
  { value: 'declined', label: 'Declined' },
  { value: 'archived', label: 'Archived' },
];

export const ACTIVE_STATUSES: CandidateStatus[] = ['new', 'in_progress', 'on_hold'];
export const COMPLETED_STATUSES: CandidateStatus[] = ['hired', 'declined'];
export const ALL_STATUSES: CandidateStatus[] = [
  'new',
  'in_progress', 
  'on_hold',
  'hired',
  'declined',
  'archived'
];

// Filter options for dropdowns (excluding archived for non-archived views)
export const getStatusFilterOptions = (includeArchived = false) => {
  return CANDIDATE_STATUS_OPTIONS.filter(
    option => includeArchived || option.value !== 'archived'
  );
};
