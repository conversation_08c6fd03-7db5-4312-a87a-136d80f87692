import { useCallback } from 'react';
import { useCandidateStore } from '../stores';
import type { CandidateStatus } from '../types';

export function useCandidateDetails(candidateId: string) {
  const {
    updateCandidateStatus,
    updateCandidateRating,
    addApplicationLog,
  } = useCandidateStore();

  const handleStatusChange = useCallback(
    (status: CandidateStatus) => {
      updateCandidateStatus(candidateId, status);
    },
    [candidateId, updateCandidateStatus]
  );

  const handleRatingChange = useCallback(
    (rating: number) => {
      updateCandidateRating(candidateId, rating);
    },
    [candidateId, updateCandidateRating]
  );

  const handleAddNote = useCallback(
    (notes: string, currentStatus: CandidateStatus) => {
      if (notes.trim()) {
        addApplicationLog(candidateId, currentStatus, notes);
        return true; // Indicates successful submission
      }
      return false;
    },
    [candidateId, addApplicationLog]
  );

  return {
    handleStatusChange,
    handleRatingChange,
    handleAddNote,
  };
}
