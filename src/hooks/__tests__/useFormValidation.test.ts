import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useFormValidation } from '../useFormValidation';
import { ValidationPatterns } from '../../utils/validation';

describe('useFormValidation', () => {
  const schema = {
    name: { required: true, minLength: 2 },
    email: { required: true, pattern: ValidationPatterns.email },
    phone: { required: false, minLength: 10 },
  };

  it('initializes with empty errors', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    expect(result.current.errors).toEqual({});
    expect(result.current.isValid).toBe(true);
  });

  it('validates individual fields', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    act(() => {
      result.current.validateField('name', '');
    });

    expect(result.current.errors.name).toBe('This field is required');
    expect(result.current.isValid).toBe(false);

    act(() => {
      result.current.validateField('name', 'John');
    });

    expect(result.current.errors.name).toBe('');
    expect(result.current.isValid).toBe(true);
  });

  it('validates entire form', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    const formData = {
      name: '',
      email: 'invalid-email',
      phone: '123',
    };

    let isValid: boolean;
    act(() => {
      isValid = result.current.validateForm(formData);
    });

    expect(isValid!).toBe(false);
    expect(result.current.errors.name).toBe('This field is required');
    expect(result.current.errors.email).toBe('Invalid format');
    expect(result.current.errors.phone).toBe('Must be at least 10 characters long');
  });

  it('returns true for valid form data', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    const formData = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '1234567890',
    };

    let isValid: boolean;
    act(() => {
      isValid = result.current.validateForm(formData);
    });

    expect(isValid!).toBe(true);
    expect(result.current.errors).toEqual({});
  });

  it('clears all errors', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    // Add some errors first
    act(() => {
      result.current.validateField('name', '');
      result.current.validateField('email', 'invalid');
    });

    expect(Object.keys(result.current.errors)).toHaveLength(2);

    act(() => {
      result.current.clearErrors();
    });

    expect(result.current.errors).toEqual({});
    expect(result.current.isValid).toBe(true);
  });

  it('clears individual field errors', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    // Add some errors first
    act(() => {
      result.current.validateField('name', '');
      result.current.validateField('email', 'invalid');
    });

    expect(result.current.errors.name).toBeTruthy();
    expect(result.current.errors.email).toBeTruthy();

    act(() => {
      result.current.clearFieldError('name');
    });

    expect(result.current.errors.name).toBeUndefined();
    expect(result.current.errors.email).toBeTruthy();
  });

  it('sets custom field errors', () => {
    const { result } = renderHook(() => 
      useFormValidation({ schema })
    );

    act(() => {
      result.current.setFieldError('name', 'Custom error message');
    });

    expect(result.current.errors.name).toBe('Custom error message');
    expect(result.current.isValid).toBe(false);
  });
});
