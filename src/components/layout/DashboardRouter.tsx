import React, { Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import { DashboardHome } from "../dashboard";

// Lazy load dashboard components
const CandidateList = React.lazy(() =>
  import("../candidate").then((module) => ({
    default: module.CandidateList,
  }))
);
const CandidateDetails = React.lazy(() =>
  import("../candidate").then((module) => ({
    default: module.CandidateDetails,
  }))
);
const AdminProfile = React.lazy(() =>
  import("../admin").then((module) => ({
    default: module.AdminProfile,
  }))
);

// Loading component for dashboard routes
function DashboardLoading() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-champagne mx-auto mb-2"></div>
        <p className="text-gray-600 text-sm">Loading...</p>
      </div>
    </div>
  );
}

export function DashboardRouter() {
  return (
    <Suspense fallback={<DashboardLoading />}>
      <Routes>
        <Route
          index
          element={<DashboardHome />}
        />
        <Route
          path="candidates"
          element={<CandidateList />}
        />
        <Route
          path="candidates/:id"
          element={<CandidateDetails />}
        />
        <Route
          path="archived"
          element={<CandidateList archived />}
        />
        <Route
          path="profile"
          element={<AdminProfile />}
        />
      </Routes>
    </Suspense>
  );
}
