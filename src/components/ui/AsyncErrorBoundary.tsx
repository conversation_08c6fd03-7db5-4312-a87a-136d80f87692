import React, { ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface AsyncErrorBoundaryProps {
  children: ReactNode;
  error: string | null;
  loading: boolean;
  onRetry?: () => void;
  fallback?: ReactNode;
}

export function AsyncErrorBoundary({
  children,
  error,
  loading,
  onRetry,
  fallback
}: AsyncErrorBoundaryProps) {
  if (error) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-[200px] flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading Data
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          {onRetry && (
            <button
              onClick={onRetry}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 bg-champagne text-white rounded-md hover:bg-champagne/90 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Retrying...' : 'Try Again'}
            </button>
          )}
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
