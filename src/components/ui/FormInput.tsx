import { AlertCircle } from "lucide-react";

interface FormInputProps {
  id: string;
  label: string;
  type?: "text" | "email" | "password" | "tel" | "number";
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  error?: string;
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function FormInput({
  id,
  label,
  type = "text",
  value,
  onChange,
  onBlur,
  error,
  required,
  placeholder,
  disabled,
  className = "",
}: FormInputProps) {
  const hasError = Boolean(error);

  return (
    <div className={`space-y-1 ${className}`}>
      <label
        htmlFor={id}
        className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        <input
          id={id}
          type={type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            input
            ${
              hasError
                ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                : "border-gray-300 focus:border-champagne focus:ring-champagne"
            }
            ${disabled ? "bg-gray-50 cursor-not-allowed" : ""}
          `}
          aria-invalid={hasError}
          aria-describedby={hasError ? `${id}-error` : undefined}
        />

        {hasError && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <AlertCircle className="h-5 w-5 text-red-500" />
          </div>
        )}
      </div>

      {hasError && (
        <p
          id={`${id}-error`}
          className="text-sm text-red-600 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </p>
      )}
    </div>
  );
}
