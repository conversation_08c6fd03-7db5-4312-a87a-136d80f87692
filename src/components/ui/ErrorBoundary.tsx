import React, { Component, ErrorInfo, ReactNode } from "react";
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from "lucide-react";
import {
  createAppError,
  createRecoveryActions,
  logError,
} from "../../utils/errorHandler";
import type { AppError, RecoveryAction } from "../../types/error";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: AppError, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  appError?: AppError;
  recoveryActions: RecoveryAction[];
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, recoveryActions: [] };
  }

  static getDerivedStateFromError(error: Error): State {
    const appError = createAppError(error, {
      component: "ErrorBoundary",
      action: "componentRender",
    });

    const recoveryActions = createRecoveryActions(appError);

    return {
      hasError: true,
      appError,
      recoveryActions,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const appError = createAppError(error, {
      component: "ErrorBoundary",
      action: "componentRender",
      additionalData: { errorInfo },
    });

    logError(appError);
    this.props.onError?.(appError, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      appError: undefined,
      recoveryActions: [],
    });
  };

  handleRecoveryAction = (action: RecoveryAction) => {
    try {
      action.action();
      // If action succeeds, reset error state
      this.setState({
        hasError: false,
        appError: undefined,
        recoveryActions: [],
      });
    } catch (error) {
      console.error("Recovery action failed:", error);
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[200px] flex items-center justify-center p-6">
          <div className="text-center max-w-md">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Something went wrong
            </h3>
            <p className="text-gray-600 mb-4">
              {this.state.appError?.userMessage ||
                "An unexpected error occurred"}
            </p>

            {/* Recovery Actions */}
            <div className="space-y-2">
              <button
                onClick={this.handleRetry}
                className="inline-flex items-center px-4 py-2 bg-champagne text-white rounded-md hover:bg-champagne/90 transition-colors mr-2">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>

              {this.state.recoveryActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => this.handleRecoveryAction(action)}
                  className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors mr-2">
                  {action.label}
                </button>
              ))}
            </div>

            {/* Technical Details (only in development) */}
            {process.env.NODE_ENV === "development" && this.state.appError && (
              <details className="mt-4 text-left">
                <summary className="text-sm text-gray-500 cursor-pointer">
                  Technical Details
                </summary>
                <pre className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(this.state.appError, null, 2)}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional wrapper for easier usage
interface ErrorBoundaryWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: AppError, errorInfo: ErrorInfo) => void;
}

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryWrapperProps, "children">
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name
  })`;
  return WrappedComponent;
}
