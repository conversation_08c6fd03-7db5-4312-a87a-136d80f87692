import type { CandidateStatus } from '../../types';

interface StatusBadgeProps {
  status: CandidateStatus;
  className?: string;
}

const statusConfig = {
  new: {
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    label: 'New'
  },
  in_progress: {
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-800',
    label: 'In Progress'
  },
  hired: {
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    label: 'Hired'
  },
  declined: {
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    label: 'Declined'
  },
  on_hold: {
    bgColor: 'bg-orange-100',
    textColor: 'text-orange-800',
    label: 'On Hold'
  },
  archived: {
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    label: 'Archived'
  }
} as const;

export function StatusBadge({ status, className = '' }: StatusBadgeProps) {
  const config = statusConfig[status] || statusConfig.archived;
  
  return (
    <span
      className={`px-2 py-1 text-xs font-medium rounded-full ${config.bgColor} ${config.textColor} ${className}`}>
      {config.label}
    </span>
  );
}
