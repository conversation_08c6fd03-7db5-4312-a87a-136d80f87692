import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../../test/utils';
import { FormInput } from '../FormInput';

describe('FormInput', () => {
  const defaultProps = {
    id: 'test-input',
    label: 'Test Label',
    value: '',
    onChange: vi.fn(),
  };

  it('renders with label and input', () => {
    render(<FormInput {...defaultProps} />);
    
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('shows required indicator when required', () => {
    render(<FormInput {...defaultProps} required />);
    
    expect(screen.getByText('*')).toBeInTheDocument();
  });

  it('displays error message when error is provided', () => {
    const error = 'This field is required';
    render(<FormInput {...defaultProps} error={error} />);
    
    expect(screen.getByText(error)).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
  });

  it('calls onChange when input value changes', () => {
    const onChange = vi.fn();
    render(<FormInput {...defaultProps} onChange={onChange} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new value' } });
    
    expect(onChange).toHaveBeenCalledWith('new value');
  });

  it('calls onBlur when input loses focus', () => {
    const onBlur = vi.fn();
    render(<FormInput {...defaultProps} onBlur={onBlur} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.blur(input);
    
    expect(onBlur).toHaveBeenCalled();
  });

  it('is disabled when disabled prop is true', () => {
    render(<FormInput {...defaultProps} disabled />);
    
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('renders different input types correctly', () => {
    const { rerender } = render(<FormInput {...defaultProps} type="email" />);
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email');

    rerender(<FormInput {...defaultProps} type="password" />);
    expect(screen.getByLabelText('Test Label')).toHaveAttribute('type', 'password');
  });
});
