import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../../test/utils';
import { ProfileForm } from '../ProfileForm';
import { mockAdminProfile } from '../../../test/mocks/data';

describe('ProfileForm', () => {
  const defaultProps = {
    adminProfile: null,
    onSubmit: vi.fn(),
    loading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders form fields', () => {
    render(<ProfileForm {...defaultProps} />);

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/job title/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /save changes/i })).toBeInTheDocument();
  });

  it('populates form with admin profile data', () => {
    render(<ProfileForm {...defaultProps} adminProfile={mockAdminProfile} />);

    expect(screen.getByDisplayValue(mockAdminProfile.name)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mockAdminProfile.title)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mockAdminProfile.email)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mockAdminProfile.phone)).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<ProfileForm {...defaultProps} loading={true} />);

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('validates required fields on submit', async () => {
    render(<ProfileForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/this field is required/i)).toBeInTheDocument();
    });

    expect(defaultProps.onSubmit).not.toHaveBeenCalled();
  });

  it('validates email format', async () => {
    render(<ProfileForm {...defaultProps} />);

    const emailInput = screen.getByLabelText(/email/i);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.blur(emailInput);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates phone format', async () => {
    render(<ProfileForm {...defaultProps} />);

    const phoneInput = screen.getByLabelText(/phone number/i);
    fireEvent.change(phoneInput, { target: { value: 'abc' } });
    fireEvent.blur(phoneInput);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    const onSubmit = vi.fn().mockResolvedValue(undefined);
    render(<ProfileForm {...defaultProps} onSubmit={onSubmit} />);

    // Fill in valid data
    fireEvent.change(screen.getByLabelText(/full name/i), {
      target: { value: 'John Doe' },
    });
    fireEvent.change(screen.getByLabelText(/job title/i), {
      target: { value: 'Manager' },
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText(/phone number/i), {
      target: { value: '1234567890' },
    });

    const submitButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        title: 'Manager',
        email: '<EMAIL>',
        phone: '1234567890',
      });
    });
  });

  it('shows success message after successful submit', async () => {
    const onSubmit = vi.fn().mockResolvedValue(undefined);
    render(<ProfileForm {...defaultProps} onSubmit={onSubmit} adminProfile={mockAdminProfile} />);

    const submitButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/profile updated successfully/i)).toBeInTheDocument();
    });
  });

  it('shows error message on submit failure', async () => {
    const onSubmit = vi.fn().mockRejectedValue(new Error('Submit failed'));
    render(<ProfileForm {...defaultProps} onSubmit={onSubmit} adminProfile={mockAdminProfile} />);

    const submitButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/failed to update profile/i)).toBeInTheDocument();
    });
  });

  it('disables submit button while submitting', async () => {
    const onSubmit = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<ProfileForm {...defaultProps} onSubmit={onSubmit} adminProfile={mockAdminProfile} />);

    const submitButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(submitButton);

    expect(screen.getByRole('button', { name: /saving/i })).toBeDisabled();

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /save changes/i })).not.toBeDisabled();
    });
  });
});
