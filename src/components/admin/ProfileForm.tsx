import React, { useState, useEffect } from "react";
import { User } from "lucide-react";
import type { AdminProfile } from "../../types";
import { useFormValidation } from "../../hooks/useFormValidation";
import { CommonValidationRules } from "../../utils/validation";
import { FormInput } from "../ui";

interface ProfileFormProps {
  adminProfile: AdminProfile | null;
  onSubmit: (profile: Partial<AdminProfile>) => Promise<void>;
  loading?: boolean;
}

const validationSchema = {
  name: CommonValidationRules.name,
  title: CommonValidationRules.required,
  email: CommonValidationRules.email,
  phone: CommonValidationRules.phone,
};

export function ProfileForm({
  adminProfile,
  onSubmit,
  loading,
}: ProfileFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    title: "",
    email: "",
    phone: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const { errors, validateField, validateForm, clearErrors } =
    useFormValidation({
      schema: validationSchema,
    });

  useEffect(() => {
    if (adminProfile) {
      setFormData({
        name: adminProfile.name || "",
        title: adminProfile.title || "",
        email: adminProfile.email || "",
        phone: adminProfile.phone || "",
      });
    }
  }, [adminProfile]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");
    clearErrors();

    // Validate form
    if (!validateForm(formData)) {
      setErrorMessage("Please fix the errors below");
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(formData);
      setSuccessMessage("Profile updated successfully");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err) {
      console.error("Error updating profile:", err);
      setErrorMessage("Failed to update profile");
      setTimeout(() => setErrorMessage(""), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFieldChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleFieldBlur = (field: string, value: string) => {
    validateField(field, value);
  };

  if (loading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  return (
    <div className="card">
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
          <User className="w-8 h-8 text-champagne" />
        </div>
        <div>
          <h2 className="text-2xl font-semibold text-dark">
            {formData.name || "Admin User"}
          </h2>
          <p className="text-gray-600">{formData.title || "Spa Manager"}</p>
        </div>
      </div>

      {successMessage && (
        <div className="mb-6 p-3 bg-green-50 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}

      {errorMessage && (
        <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
          {errorMessage}
        </div>
      )}

      <form
        onSubmit={handleSubmit}
        className="space-y-6">
        <FormInput
          id="name"
          label="Full Name"
          type="text"
          value={formData.name}
          onChange={(value) => handleFieldChange("name", value)}
          onBlur={() => handleFieldBlur("name", formData.name)}
          error={errors.name}
          required
        />

        <FormInput
          id="title"
          label="Job Title"
          type="text"
          value={formData.title}
          onChange={(value) => handleFieldChange("title", value)}
          onBlur={() => handleFieldBlur("title", formData.title)}
          error={errors.title}
          required
        />

        <FormInput
          id="email"
          label="Email"
          type="email"
          value={formData.email}
          onChange={(value) => handleFieldChange("email", value)}
          onBlur={() => handleFieldBlur("email", formData.email)}
          error={errors.email}
          required
        />

        <FormInput
          id="phone"
          label="Phone Number"
          type="tel"
          value={formData.phone}
          onChange={(value) => handleFieldChange("phone", value)}
          onBlur={() => handleFieldBlur("phone", formData.phone)}
          error={errors.phone}
          required
        />

        <div>
          <button
            type="submit"
            className="btn-primary"
            disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </form>
    </div>
  );
}
