import { useState, useEffect } from "react";
import { Bell } from "lucide-react";
import type { AdminPreferences } from "../../types";

interface NotificationSettingsProps {
  adminPreferences: AdminPreferences | null;
  onUpdate: (preferences: Partial<AdminPreferences>) => Promise<void>;
  loading?: boolean;
}

export function NotificationSettings({
  adminPreferences,
  onUpdate,
  loading,
}: NotificationSettingsProps) {
  const [notificationPrefs, setNotificationPrefs] = useState({
    email_notifications: true,
    new_applications: true,
    status_updates: true,
    daily_digest: false,
  });

  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    if (adminPreferences) {
      setNotificationPrefs({
        email_notifications: adminPreferences.email_notifications,
        new_applications: adminPreferences.new_applications,
        status_updates: adminPreferences.status_updates,
        daily_digest: adminPreferences.daily_digest,
      });
    }
  }, [adminPreferences]);

  const handleNotificationChange = async (
    key: keyof typeof notificationPrefs
  ) => {
    const updatedPrefs = {
      ...notificationPrefs,
      [key]: !notificationPrefs[key],
    };
    setNotificationPrefs(updatedPrefs);

    try {
      await onUpdate(updatedPrefs);
    } catch (err) {
      console.error("Error updating preferences:", err);
      setErrorMessage("Failed to update notification preferences");
      setTimeout(() => setErrorMessage(""), 3000);
      // Revert the change on error
      setNotificationPrefs(notificationPrefs);
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  return (
    <div className="card">
      <div className="flex items-center space-x-3 mb-6">
        <Bell className="w-6 h-6 text-champagne" />
        <h3 className="text-xl font-semibold text-dark">
          Notification Preferences
        </h3>
      </div>

      {errorMessage && (
        <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
          {errorMessage}
        </div>
      )}

      <div className="space-y-4">
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={notificationPrefs.email_notifications}
            onChange={() => handleNotificationChange("email_notifications")}
            className="rounded border-gray-300 text-champagne focus:ring-champagne"
          />
          <span className="text-gray-700">Enable Email Notifications</span>
        </label>

        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={notificationPrefs.new_applications}
            onChange={() => handleNotificationChange("new_applications")}
            className="rounded border-gray-300 text-champagne focus:ring-champagne"
          />
          <span className="text-gray-700">New Application Alerts</span>
        </label>

        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={notificationPrefs.status_updates}
            onChange={() => handleNotificationChange("status_updates")}
            className="rounded border-gray-300 text-champagne focus:ring-champagne"
          />
          <span className="text-gray-700">Status Update Notifications</span>
        </label>

        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={notificationPrefs.daily_digest}
            onChange={() => handleNotificationChange("daily_digest")}
            className="rounded border-gray-300 text-champagne focus:ring-champagne"
          />
          <span className="text-gray-700">Daily Digest Email</span>
        </label>
      </div>
    </div>
  );
}
