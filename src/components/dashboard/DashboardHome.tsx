import React, { useMemo } from "react";
import { <PERSON> } from "react-router-dom";
import { Users, Star, Archive } from "lucide-react";
import { useCandidateStore } from "../../stores";
import { StatusBadge } from "../ui";

export function DashboardHome() {
  const { candidates, fetchCandidates } = useCandidateStore();

  React.useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);

  // Calculate metrics from real data - memoized for performance
  const metrics = useMemo(() => {
    const activeApplications = candidates.filter((c) =>
      ["new", "in_progress", "on_hold"].includes(c.status)
    ).length;

    const totalHires = candidates.filter((c) => c.status === "hired").length;
    const archivedCount = candidates.filter(
      (c) => c.status === "archived"
    ).length;

    return { activeApplications, totalHires, archivedCount };
  }, [candidates]);

  // Get recent candidates (last 5) - memoized for performance
  const recentCandidates = useMemo(() => {
    return candidates.filter((c) => c.status !== "archived").slice(0, 5);
  }, [candidates]);

  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center">
            <Users className="w-10 h-10 text-champagne" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-dark">
                Active Applications
              </h3>
              <p className="text-3xl font-bold text-champagne">
                {metrics.activeApplications}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <Star className="w-10 h-10 text-champagne" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-dark">Total Hires</h3>
              <p className="text-3xl font-bold text-champagne">
                {metrics.totalHires}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <Archive className="w-10 h-10 text-champagne" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-dark">Archived</h3>
              <p className="text-3xl font-bold text-champagne">
                {metrics.archivedCount}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Candidates */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-dark">Recent Candidates</h3>
          <Link
            to="/dashboard/candidates"
            className="text-champagne hover:text-champagne/80">
            View All
          </Link>
        </div>

        <div className="space-y-4">
          {recentCandidates.length > 0 ? (
            recentCandidates.map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-dark">
                    {candidate.full_name}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {candidate.position?.title}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <StatusBadge status={candidate.status} />
                  <Link
                    to={`/dashboard/candidates/${candidate.id}`}
                    className="text-champagne hover:text-champagne/80 text-sm">
                    View
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500">No recent candidates found.</p>
          )}
        </div>
      </div>
    </div>
  );
}
