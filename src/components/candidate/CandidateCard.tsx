import { Link } from 'react-router-dom';
import { Flag, Star } from 'lucide-react';
import { StatusBadge } from '../ui';
import type { Candidate } from '../../types';

interface CandidateCardProps {
  candidate: Candidate;
}

export function CandidateCard({ candidate }: CandidateCardProps) {
  return (
    <div className="card hover:shadow-lg transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-dark">
            {candidate.full_name}
            {candidate.is_new && (
              <span className="ml-2 px-2 py-1 text-xs bg-champagne/10 text-champagne rounded-full">
                New
              </span>
            )}
          </h3>
          <p className="text-gray-600">{candidate.position?.title}</p>
        </div>
        <button className="text-gray-400 hover:text-champagne">
          <Flag className="w-5 h-5" />
        </button>
      </div>

      <div className="flex items-center space-x-1 mb-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className="w-4 h-4 text-champagne"
            fill={star <= candidate.rating ? 'currentColor' : 'none'}
          />
        ))}
      </div>

      <div className="flex items-center justify-between">
        <StatusBadge status={candidate.status} />
        <Link
          to={`/dashboard/candidates/${candidate.id}`}
          className="text-champagne hover:text-champagne/80">
          View Details
        </Link>
      </div>
    </div>
  );
}
