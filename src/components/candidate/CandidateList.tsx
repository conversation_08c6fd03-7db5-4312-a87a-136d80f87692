import React, { useEffect, useMemo, useCallback } from "react";
import { useCandidateStore, useCommonStore } from "../../stores";
import { AsyncErrorBoundary } from "../ui";
import { CandidateFilters, CandidateCard } from "./";

export function CandidateList({ archived = false }) {
  const {
    candidates,
    filters,
    loading: candidateLoading,
    error: candidateError,
    fetchCandidates,
    setFilters,
  } = useCandidateStore();

  const {
    positions,
    locations,
    loading: commonLoading,
    error: commonError,
    fetchPositions,
    fetchLocations,
  } = useCommonStore();

  const loading = candidateLoading || commonLoading;
  const error = candidateError || commonError;

  useEffect(() => {
    fetchCandidates();
    fetchPositions();
    fetchLocations();
  }, [fetchCandidates, fetchPositions, fetchLocations]);

  // Memoize filtered candidates for performance
  const filteredCandidates = useMemo(() => {
    return candidates.filter((candidate) => {
      if (archived && candidate.status !== "archived") return false;
      if (!archived && candidate.status === "archived") return false;

      if (filters.position && candidate.position_id !== filters.position)
        return false;
      if (filters.location && candidate.location_id !== filters.location)
        return false;
      if (filters.status && candidate.status !== filters.status) return false;

      return true;
    });
  }, [candidates, archived, filters]);

  const handleRetry = useCallback(() => {
    fetchCandidates();
    fetchPositions();
    fetchLocations();
  }, [fetchCandidates, fetchPositions, fetchLocations]);

  return (
    <AsyncErrorBoundary
      error={error}
      loading={loading}
      onRetry={handleRetry}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-dark">
            {archived ? "Archived Candidates" : "All Candidates"}
          </h2>

          <CandidateFilters
            filters={filters}
            positions={positions}
            locations={locations}
            archived={archived}
            onFiltersChange={setFilters}
          />
        </div>

        {/* Candidate Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCandidates.map((candidate) => (
            <CandidateCard
              key={candidate.id}
              candidate={candidate}
            />
          ))}
        </div>
      </div>
    </AsyncErrorBoundary>
  );
}
