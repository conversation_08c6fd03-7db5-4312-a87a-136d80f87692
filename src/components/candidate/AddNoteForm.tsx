import React, { useState } from 'react';
import type { CandidateStatus } from '../../types';

interface AddNoteFormProps {
  onSubmit: (notes: string) => boolean;
  loading?: boolean;
}

export function AddNoteForm({ onSubmit, loading = false }: AddNoteFormProps) {
  const [notes, setNotes] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const success = onSubmit(notes);
    if (success) {
      setNotes(''); // Clear form on successful submission
    }
  };

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-dark mb-4">Add Note</h3>
      <form onSubmit={handleSubmit}>
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          className="input min-h-[150px]"
          placeholder="Add a note..."
          disabled={loading}
        />
        <button
          type="submit"
          className="btn-primary mt-4"
          disabled={loading || !notes.trim()}>
          {loading ? 'Saving...' : 'Save Note'}
        </button>
      </form>
    </div>
  );
}
