import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import { Star, Phone, Mail, MapPin, Calendar } from "lucide-react";
import { useCandidateStore } from "../../stores";
import { useCandidateDetails } from "../../hooks/useCandidateDetails";
import { AddNoteForm } from "./AddNoteForm";
import { AsyncErrorBoundary } from "../ui";
import { CANDIDATE_STATUS_OPTIONS } from "../../constants";
import { format } from "date-fns";
import type { CandidateStatus } from "../../types";

export function CandidateDetails() {
  const { id } = useParams();
  const {
    candidates,
    applicationLogs,
    loading,
    error,
    fetchCandidates,
    fetchApplicationLogs,
  } = useCandidateStore();

  const { handleStatusChange, handleRatingChange, handleAddNote } =
    useCandidateDetails(id || "");

  useEffect(() => {
    fetchCandidates();
    if (id) {
      fetchApplicationLogs(id);
    }
  }, [id, fetchCandidates, fetchApplicationLogs]);

  const candidate = candidates.find((c) => c.id === id);

  const handleRetry = () => {
    fetchCandidates();
    if (id) {
      fetchApplicationLogs(id);
    }
  };

  if (!candidate && !loading) {
    return <div className="text-center py-8">Candidate not found</div>;
  }

  return (
    <AsyncErrorBoundary
      error={error}
      loading={loading}
      onRetry={handleRetry}>
      {candidate && (
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-dark">
                {candidate.full_name}
              </h2>
              <p className="text-gray-600">{candidate.position?.title}</p>
            </div>

            <select
              className="input max-w-xs"
              value={candidate.status}
              onChange={(e) =>
                handleStatusChange(e.target.value as CandidateStatus)
              }>
              {CANDIDATE_STATUS_OPTIONS.map((option) => (
                <option
                  key={option.value}
                  value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Info */}
            <div className="lg:col-span-2 space-y-6">
              <div className="card">
                <h3 className="text-lg font-semibold text-dark mb-4">
                  Contact Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600">
                    <Mail className="w-5 h-5 mr-2" />
                    {candidate.email}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Phone className="w-5 h-5 mr-2" />
                    {candidate.phone}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <MapPin className="w-5 h-5 mr-2" />
                    {candidate.location?.name}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Calendar className="w-5 h-5 mr-2" />
                    {candidate.availability?.join(", ") || "Not specified"}
                  </div>
                </div>
              </div>

              <div className="card">
                <h3 className="text-lg font-semibold text-dark mb-4">
                  Application Timeline
                </h3>
                <div className="space-y-4">
                  {applicationLogs.map((log) => (
                    <div
                      key={log.id}
                      className="flex items-start">
                      <div className="w-2 h-2 mt-2 rounded-full bg-champagne"></div>
                      <div className="ml-4">
                        <p className="font-medium text-dark">{log.notes}</p>
                        <p className="text-sm text-gray-600">
                          {format(new Date(log.created_at), "MMMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <div className="card">
                <h3 className="text-lg font-semibold text-dark mb-4">Rating</h3>
                <div className="flex items-center space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => handleRatingChange(star)}>
                      <Star
                        className="w-6 h-6 text-champagne"
                        fill={
                          star <= candidate.rating ? "currentColor" : "none"
                        }
                      />
                    </button>
                  ))}
                </div>
              </div>

              <AddNoteForm
                onSubmit={(notes) => handleAddNote(notes, candidate.status)}
                loading={loading}
              />
            </div>
          </div>
        </div>
      )}
    </AsyncErrorBoundary>
  );
}
