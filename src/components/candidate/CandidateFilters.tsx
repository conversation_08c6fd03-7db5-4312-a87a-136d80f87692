import type { Position, Location, StoreFilters } from '../../types';
import { getStatusFilterOptions } from '../../constants';

interface CandidateFiltersProps {
  filters: StoreFilters;
  positions: Position[];
  locations: Location[];
  archived: boolean;
  onFiltersChange: (filters: Partial<StoreFilters>) => void;
}

export function CandidateFilters({
  filters,
  positions,
  locations,
  archived,
  onFiltersChange,
}: CandidateFiltersProps) {
  return (
    <div className="flex items-center space-x-4">
      <select
        className="input max-w-xs"
        value={filters.position}
        onChange={(e) => onFiltersChange({ position: e.target.value })}>
        <option value="">All Positions</option>
        {positions.map((position) => (
          <option key={position.id} value={position.id}>
            {position.title}
          </option>
        ))}
      </select>

      <select
        className="input max-w-xs"
        value={filters.location}
        onChange={(e) => onFiltersChange({ location: e.target.value })}>
        <option value="">All Locations</option>
        {locations.map((location) => (
          <option key={location.id} value={location.id}>
            {location.name}
          </option>
        ))}
      </select>

      <select
        className="input max-w-xs"
        value={filters.status}
        onChange={(e) => onFiltersChange({ status: e.target.value })}>
        <option value="">All Statuses</option>
        {getStatusFilterOptions(!archived).map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
}
