import { create } from 'zustand';
import { candidateService } from '../services';
import type { Candidate, CandidateStatus, ApplicationLog, StoreFilters } from '../types';

interface CandidateStoreState {
  candidates: Candidate[];
  applicationLogs: ApplicationLog[];
  filters: StoreFilters;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchCandidates: () => Promise<void>;
  updateCandidateStatus: (candidateId: string, status: CandidateStatus) => Promise<void>;
  updateCandidateRating: (candidateId: string, rating: number) => Promise<void>;
  fetchApplicationLogs: (candidateId: string) => Promise<void>;
  addApplicationLog: (candidateId: string, status: CandidateStatus, notes: string) => Promise<void>;
  setFilters: (filters: Partial<StoreFilters>) => void;
  clearError: () => void;
}

export const useCandidateStore = create<CandidateStoreState>((set, get) => ({
  candidates: [],
  applicationLogs: [],
  filters: {
    position: '',
    location: '',
    status: '',
  },
  loading: false,
  error: null,

  fetchCandidates: async () => {
    set({ loading: true, error: null });
    try {
      const candidates = await candidateService.fetchCandidates();
      set({ candidates });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ loading: false });
    }
  },

  updateCandidateStatus: async (candidateId: string, status: CandidateStatus) => {
    try {
      set({ error: null });
      await candidateService.updateCandidateStatus(candidateId, status);
      
      // Add status change to application logs
      await get().addApplicationLog(candidateId, status, `Status updated to ${status}`);
      await get().fetchCandidates();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateCandidateRating: async (candidateId: string, rating: number) => {
    try {
      set({ error: null });
      await candidateService.updateCandidateRating(candidateId, rating);
      await get().fetchCandidates();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchApplicationLogs: async (candidateId: string) => {
    try {
      set({ error: null });
      const applicationLogs = await candidateService.fetchApplicationLogs(candidateId);
      set({ applicationLogs });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  addApplicationLog: async (candidateId: string, status: CandidateStatus, notes: string) => {
    try {
      set({ error: null });
      await candidateService.addApplicationLog(candidateId, status, notes);
      await get().fetchApplicationLogs(candidateId);
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  setFilters: (filters) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
  },

  clearError: () => set({ error: null }),
}));
