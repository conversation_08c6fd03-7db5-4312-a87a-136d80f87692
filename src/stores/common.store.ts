import { create } from 'zustand';
import { commonService } from '../services';
import type { Position, Location } from '../types';

interface CommonStoreState {
  positions: Position[];
  locations: Location[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchPositions: () => Promise<void>;
  fetchLocations: () => Promise<void>;
  clearError: () => void;
}

export const useCommonStore = create<CommonStoreState>((set) => ({
  positions: [],
  locations: [],
  loading: false,
  error: null,

  fetchPositions: async () => {
    set({ loading: true, error: null });
    try {
      const positions = await commonService.fetchPositions();
      set({ positions });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ loading: false });
    }
  },

  fetchLocations: async () => {
    set({ loading: true, error: null });
    try {
      const locations = await commonService.fetchLocations();
      set({ locations });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
