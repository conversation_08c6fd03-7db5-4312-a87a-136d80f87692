import { create } from "zustand";
import { commonService } from "../services";
import { createAppError, createRecoveryActions } from "../utils/errorHandler";
import { RecoveryStrategy } from "../types/error";
import type { Position, Location } from "../types";
import type { AppError, RecoveryAction } from "../types/error";

interface CommonStoreState {
  positions: Position[];
  locations: Location[];
  loading: boolean;
  error: AppError | null;
  recoveryActions: RecoveryAction[];
  lastFailedAction: (() => Promise<void>) | null;

  // Actions
  fetchPositions: () => Promise<void>;
  fetchLocations: () => Promise<void>;
  clearError: () => void;
  retryLastAction: () => Promise<void>;
}

export const useCommonStore = create<CommonStoreState>((set, get) => ({
  positions: [],
  locations: [],
  loading: false,
  error: null,
  recoveryActions: [],
  lastFailedAction: null,

  fetchPositions: async () => {
    set({ loading: true, error: null, recoveryActions: [] });
    try {
      const positions = await commonService.fetchPositions();
      set({ positions, lastFailedAction: null });
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "CommonStore",
              action: "fetchPositions",
            })
          : error;

      const recoveryActions = createRecoveryActions(appError);
      recoveryActions.unshift({
        strategy: RecoveryStrategy.RETRY,
        label: "Retry",
        action: () => get().fetchPositions(),
      });

      set({
        error: appError,
        recoveryActions,
        lastFailedAction: () => get().fetchPositions(),
      });
    } finally {
      set({ loading: false });
    }
  },

  fetchLocations: async () => {
    set({ loading: true, error: null, recoveryActions: [] });
    try {
      const locations = await commonService.fetchLocations();
      set({ locations, lastFailedAction: null });
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "CommonStore",
              action: "fetchLocations",
            })
          : error;

      const recoveryActions = createRecoveryActions(appError);
      recoveryActions.unshift({
        strategy: RecoveryStrategy.RETRY,
        label: "Retry",
        action: () => get().fetchLocations(),
      });

      set({
        error: appError,
        recoveryActions,
        lastFailedAction: () => get().fetchLocations(),
      });
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null, recoveryActions: [] }),

  retryLastAction: async () => {
    const { lastFailedAction } = get();
    if (lastFailedAction) {
      await lastFailedAction();
    }
  },
}));
