import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCandidateStore } from '../candidate.store';
import { mockCandidates } from '../../test/mocks/data';

// Mock the candidate service
vi.mock('../../services/candidate.service', () => ({
  candidateService: {
    fetchCandidates: vi.fn(),
    updateCandidateStatus: vi.fn(),
    updateCandidateRating: vi.fn(),
    fetchApplicationLogs: vi.fn(),
    addApplicationLog: vi.fn(),
  },
}));

describe('useCandidateStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useCandidateStore.setState({
      candidates: [],
      applicationLogs: [],
      filters: { position: '', location: '', status: '' },
      loading: false,
      error: null,
    });
  });

  it('initializes with default state', () => {
    const { result } = renderHook(() => useCandidateStore());

    expect(result.current.candidates).toEqual([]);
    expect(result.current.applicationLogs).toEqual([]);
    expect(result.current.filters).toEqual({
      position: '',
      location: '',
      status: '',
    });
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('fetches candidates successfully', async () => {
    const { candidateService } = await import('../../services/candidate.service');
    vi.mocked(candidateService.fetchCandidates).mockResolvedValue(mockCandidates);

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.fetchCandidates();
    });

    expect(result.current.candidates).toEqual(mockCandidates);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('handles fetch candidates error', async () => {
    const { candidateService } = await import('../../services/candidate.service');
    const errorMessage = 'Failed to fetch candidates';
    vi.mocked(candidateService.fetchCandidates).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.fetchCandidates();
    });

    expect(result.current.candidates).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(errorMessage);
  });

  it('updates candidate status', async () => {
    const { candidateService } = await import('../../services/candidate.service');
    vi.mocked(candidateService.updateCandidateStatus).mockResolvedValue();
    vi.mocked(candidateService.addApplicationLog).mockResolvedValue();
    vi.mocked(candidateService.fetchCandidates).mockResolvedValue(mockCandidates);

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.updateCandidateStatus('1', 'in_progress');
    });

    expect(candidateService.updateCandidateStatus).toHaveBeenCalledWith('1', 'in_progress');
    expect(candidateService.addApplicationLog).toHaveBeenCalledWith(
      '1',
      'in_progress',
      'Status updated to in_progress'
    );
  });

  it('updates filters', () => {
    const { result } = renderHook(() => useCandidateStore());

    act(() => {
      result.current.setFilters({ position: '1', status: 'new' });
    });

    expect(result.current.filters).toEqual({
      position: '1',
      location: '',
      status: 'new',
    });
  });

  it('clears errors', () => {
    const { result } = renderHook(() => useCandidateStore());

    // Set an error first
    act(() => {
      useCandidateStore.setState({ error: 'Some error' });
    });

    expect(result.current.error).toBe('Some error');

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeNull();
  });

  it('updates candidate rating', async () => {
    const { candidateService } = await import('../../services/candidate.service');
    vi.mocked(candidateService.updateCandidateRating).mockResolvedValue();
    vi.mocked(candidateService.fetchCandidates).mockResolvedValue(mockCandidates);

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.updateCandidateRating('1', 5);
    });

    expect(candidateService.updateCandidateRating).toHaveBeenCalledWith('1', 5);
    expect(candidateService.fetchCandidates).toHaveBeenCalled();
  });
});
