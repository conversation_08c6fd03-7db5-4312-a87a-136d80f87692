import { create } from 'zustand';
import { adminService } from '../services';
import type { AdminProfile, AdminPreferences } from '../types';

interface AdminStoreState {
  adminProfile: AdminProfile | null;
  adminPreferences: AdminPreferences | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchAdminProfile: () => Promise<void>;
  updateAdminProfile: (profile: Partial<AdminProfile>) => Promise<void>;
  fetchAdminPreferences: () => Promise<void>;
  updateAdminPreferences: (preferences: Partial<AdminPreferences>) => Promise<void>;
  clearError: () => void;
}

export const useAdminStore = create<AdminStoreState>((set, get) => ({
  adminProfile: null,
  adminPreferences: null,
  loading: false,
  error: null,

  fetchAdminProfile: async () => {
    set({ loading: true, error: null });
    try {
      const adminProfile = await adminService.fetchAdminProfile();
      set({ adminProfile });
    } catch (error: any) {
      set({ error: error.message });
    } finally {
      set({ loading: false });
    }
  },

  updateAdminProfile: async (profile: Partial<AdminProfile>) => {
    try {
      set({ error: null });
      await adminService.updateAdminProfile(profile);
      await get().fetchAdminProfile();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchAdminPreferences: async () => {
    try {
      set({ error: null });
      const adminPreferences = await adminService.fetchAdminPreferences();
      set({ adminPreferences });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateAdminPreferences: async (preferences: Partial<AdminPreferences>) => {
    try {
      set({ error: null });
      await adminService.updateAdminPreferences(preferences);
      await get().fetchAdminPreferences();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  clearError: () => set({ error: null }),
}));
