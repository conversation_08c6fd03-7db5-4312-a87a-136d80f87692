import { create } from "zustand";
import { adminService } from "../services";
import { createAppError, createRecoveryActions } from "../utils/errorHandler";
import { RecoveryStrategy } from "../types/error";
import type { AdminProfile, AdminPreferences } from "../types";
import type { AppError, RecoveryAction } from "../types/error";

interface AdminStoreState {
  adminProfile: AdminProfile | null;
  adminPreferences: AdminPreferences | null;
  loading: boolean;
  error: AppError | null;
  recoveryActions: RecoveryAction[];
  lastFailedAction: (() => Promise<void>) | null;

  // Actions
  fetchAdminProfile: () => Promise<void>;
  updateAdminProfile: (profile: Partial<AdminProfile>) => Promise<void>;
  fetchAdminPreferences: () => Promise<void>;
  updateAdminPreferences: (
    preferences: Partial<AdminPreferences>
  ) => Promise<void>;
  clearError: () => void;
  retryLastAction: () => Promise<void>;
}

export const useAdminStore = create<AdminStoreState>((set, get) => ({
  adminProfile: null,
  adminPreferences: null,
  loading: false,
  error: null,
  recoveryActions: [],
  lastFailedAction: null,

  fetchAdminProfile: async () => {
    set({ loading: true, error: null, recoveryActions: [] });
    try {
      const adminProfile = await adminService.fetchAdminProfile();
      set({ adminProfile, lastFailedAction: null });
    } catch (error: any) {
      const appError =
        error instanceof Error
          ? createAppError(error, {
              component: "AdminStore",
              action: "fetchAdminProfile",
            })
          : error;

      const recoveryActions = createRecoveryActions(appError);
      recoveryActions.unshift({
        strategy: RecoveryStrategy.RETRY,
        label: "Retry",
        action: () => get().fetchAdminProfile(),
      });

      set({
        error: appError,
        recoveryActions,
        lastFailedAction: () => get().fetchAdminProfile(),
      });
    } finally {
      set({ loading: false });
    }
  },

  updateAdminProfile: async (profile: Partial<AdminProfile>) => {
    try {
      set({ error: null });
      await adminService.updateAdminProfile(profile);
      await get().fetchAdminProfile();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  fetchAdminPreferences: async () => {
    try {
      set({ error: null });
      const adminPreferences = await adminService.fetchAdminPreferences();
      set({ adminPreferences });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  updateAdminPreferences: async (preferences: Partial<AdminPreferences>) => {
    try {
      set({ error: null });
      await adminService.updateAdminPreferences(preferences);
      await get().fetchAdminPreferences();
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  clearError: () => set({ error: null, recoveryActions: [] }),

  retryLastAction: async () => {
    const { lastFailedAction } = get();
    if (lastFailedAction) {
      await lastFailedAction();
    }
  },
}));
