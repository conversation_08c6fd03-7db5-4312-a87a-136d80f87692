/**
 * Environment variable validation utility
 * Ensures all required environment variables are present and valid
 */

interface EnvConfig {
  VITE_SUPABASE_URL: string;
  VITE_SUPABASE_ANON_KEY: string;
  NODE_ENV: 'development' | 'production' | 'test';
  MODE?: string;
  DEV?: boolean;
  PROD?: boolean;
}

/**
 * Required environment variables
 */
const REQUIRED_ENV_VARS = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
] as const;

/**
 * Validate a single environment variable
 */
function validateEnvVar(name: string, value: string | undefined): string {
  if (!value || value.trim() === '') {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value.trim();
}

/**
 * Validate URL format
 */
function validateUrl(name: string, value: string): string {
  try {
    new URL(value);
    return value;
  } catch {
    throw new Error(`Invalid URL format for environment variable: ${name}`);
  }
}

/**
 * Validate and return environment configuration
 */
export function validateEnvironment(): EnvConfig {
  const errors: string[] = [];
  
  try {
    // Validate required variables
    const VITE_SUPABASE_URL = validateEnvVar('VITE_SUPABASE_URL', import.meta.env.VITE_SUPABASE_URL);
    const VITE_SUPABASE_ANON_KEY = validateEnvVar('VITE_SUPABASE_ANON_KEY', import.meta.env.VITE_SUPABASE_ANON_KEY);
    
    // Validate URL format
    validateUrl('VITE_SUPABASE_URL', VITE_SUPABASE_URL);
    
    // Validate NODE_ENV
    const NODE_ENV = import.meta.env.NODE_ENV as EnvConfig['NODE_ENV'];
    if (!['development', 'production', 'test'].includes(NODE_ENV)) {
      errors.push(`Invalid NODE_ENV: ${NODE_ENV}. Must be 'development', 'production', or 'test'`);
    }
    
    if (errors.length > 0) {
      throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
    }
    
    return {
      VITE_SUPABASE_URL,
      VITE_SUPABASE_ANON_KEY,
      NODE_ENV,
      MODE: import.meta.env.MODE,
      DEV: import.meta.env.DEV,
      PROD: import.meta.env.PROD,
    };
    
  } catch (error) {
    if (error instanceof Error) {
      console.error('❌ Environment validation failed:', error.message);
      
      // In development, show helpful error message
      if (import.meta.env.DEV) {
        console.error('\n📝 To fix this, create a .env file in your project root with:');
        console.error('VITE_SUPABASE_URL=your_supabase_url');
        console.error('VITE_SUPABASE_ANON_KEY=your_supabase_anon_key');
      }
    }
    throw error;
  }
}

/**
 * Get validated environment configuration
 * Cached after first validation
 */
let cachedEnv: EnvConfig | null = null;

export function getEnv(): EnvConfig {
  if (!cachedEnv) {
    cachedEnv = validateEnvironment();
  }
  return cachedEnv;
}

/**
 * Check if running in development mode
 */
export function isDevelopment(): boolean {
  return getEnv().NODE_ENV === 'development';
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
  return getEnv().NODE_ENV === 'production';
}

/**
 * Check if running in test mode
 */
export function isTest(): boolean {
  return getEnv().NODE_ENV === 'test';
}
