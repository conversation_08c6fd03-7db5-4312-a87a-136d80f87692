import React from "react";
import { RetryConfig, DEFAULT_RETRY_CONFIG, AppError } from "../types/error";
import { calculateRetryDelay, createAppError, logError } from "./errorHandler";

interface RetryState {
  attempt: number;
  lastError: AppError | null;
  isRetrying: boolean;
}

// Retry function with exponential backoff
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  context?: { component?: string; action?: string }
): Promise<T> {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: AppError | null = null;

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      const appError = createAppError(
        error instanceof Error ? error : new Error(String(error)),
        context
      );

      lastError = appError;
      logError(appError);

      // Don't retry if not retryable or max attempts reached
      if (!appError.retryable || attempt === retryConfig.maxRetries) {
        throw appError;
      }

      // Calculate delay and wait before retry
      const delay = calculateRetryDelay(attempt, retryConfig);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // This should never be reached, but TypeScript requires it
  throw lastError || new Error("Retry failed");
}

// Retry hook for React components
export function useRetry() {
  const [retryState, setRetryState] = React.useState<RetryState>({
    attempt: 0,
    lastError: null,
    isRetrying: false,
  });

  const retry = React.useCallback(
    async <T>(
      operation: () => Promise<T>,
      config?: Partial<RetryConfig>
    ): Promise<T> => {
      setRetryState((prev) => ({ ...prev, isRetrying: true }));

      try {
        const result = await withRetry(operation, config);
        setRetryState({ attempt: 0, lastError: null, isRetrying: false });
        return result;
      } catch (error) {
        const appError =
          error instanceof Error ? createAppError(error) : (error as AppError);
        setRetryState({
          attempt: retryState.attempt + 1,
          lastError: appError,
          isRetrying: false,
        });
        throw appError;
      }
    },
    [retryState.attempt]
  );

  const reset = React.useCallback(() => {
    setRetryState({ attempt: 0, lastError: null, isRetrying: false });
  }, []);

  return {
    ...retryState,
    retry,
    reset,
  };
}

// Circuit breaker pattern for preventing cascading failures
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private threshold: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === "OPEN") {
      if (Date.now() - this.lastFailureTime < this.timeout) {
        throw new Error("Circuit breaker is OPEN");
      }
      this.state = "HALF_OPEN";
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = "CLOSED";
  }

  private onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.threshold) {
      this.state = "OPEN";
    }
  }

  getState() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    };
  }
}

export const circuitBreaker = new CircuitBreaker();

// Batch operation with partial failure handling
export async function withBatchRetry<T, R>(
  items: T[],
  operation: (item: T) => Promise<R>,
  config: Partial<RetryConfig> = {}
): Promise<{ successes: R[]; failures: { item: T; error: AppError }[] }> {
  const successes: R[] = [];
  const failures: { item: T; error: AppError }[] = [];

  await Promise.allSettled(
    items.map(async (item) => {
      try {
        const result = await withRetry(() => operation(item), config);
        successes.push(result);
      } catch (error) {
        failures.push({
          item,
          error:
            error instanceof Error
              ? createAppError(error)
              : (error as AppError),
        });
      }
    })
  );

  return { successes, failures };
}

// Timeout wrapper
export function withTimeout<T>(
  operation: () => Promise<T>,
  timeoutMs: number = 30000
): Promise<T> {
  return Promise.race([
    operation(),
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error("Operation timed out")), timeoutMs)
    ),
  ]);
}
