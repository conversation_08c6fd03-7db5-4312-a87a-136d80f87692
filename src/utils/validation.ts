export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string;
}

export function validateField(value: any, rule: ValidationRule): string | null {
  // Required validation
  if (
    rule.required &&
    (!value || (typeof value === "string" && value.trim() === ""))
  ) {
    return "This field is required";
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === "string" && value.trim() === "")) {
    return null;
  }

  // Custom validation (check first to allow custom error messages)
  if (rule.custom) {
    const customError = rule.custom(value);
    if (customError) {
      return customError;
    }
  }

  // String validations
  if (typeof value === "string") {
    // Min length validation
    if (rule.minLength && value.length < rule.minLength) {
      return `Must be at least ${rule.minLength} characters long`;
    }

    // Max length validation
    if (rule.maxLength && value.length > rule.maxLength) {
      return `Must be no more than ${rule.maxLength} characters long`;
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(value)) {
      return "Invalid format";
    }
  }

  return null;
}

export function validateForm(
  data: Record<string, any>,
  schema: ValidationSchema
): ValidationErrors {
  const errors: ValidationErrors = {};

  for (const [field, rule] of Object.entries(schema)) {
    const error = validateField(data[field], rule);
    if (error) {
      errors[field] = error;
    }
  }

  return errors;
}

export function hasValidationErrors(errors: ValidationErrors): boolean {
  return Object.keys(errors).length > 0;
}

// Common validation patterns
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  name: /^[a-zA-Z\s'-]{2,50}$/,
};

// Common validation rules
export const CommonValidationRules = {
  required: { required: true },
  email: {
    required: true,
    pattern: ValidationPatterns.email,
    custom: (value: string) => {
      if (value && !ValidationPatterns.email.test(value)) {
        return "Please enter a valid email address";
      }
      return null;
    },
  },
  phone: {
    required: true,
    pattern: ValidationPatterns.phone,
    custom: (value: string) => {
      if (value && !ValidationPatterns.phone.test(value)) {
        return "Please enter a valid phone number";
      }
      return null;
    },
  },
  password: {
    required: true,
    minLength: 8,
    custom: (value: string) => {
      if (value && !ValidationPatterns.password.test(value)) {
        return "Password must contain at least 8 characters with uppercase, lowercase, and number";
      }
      return null;
    },
  },
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: ValidationPatterns.name,
    custom: (value: string) => {
      if (value && !ValidationPatterns.name.test(value)) {
        return "Name can only contain letters, spaces, hyphens, and apostrophes";
      }
      return null;
    },
  },
};
