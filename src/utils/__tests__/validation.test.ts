import { describe, it, expect } from 'vitest';
import { 
  validateField, 
  validateForm, 
  hasValidationErrors,
  ValidationPatterns,
  CommonValidationRules 
} from '../validation';

describe('validation utils', () => {
  describe('validateField', () => {
    it('validates required fields', () => {
      const rule = { required: true };
      
      expect(validateField('', rule)).toBe('This field is required');
      expect(validateField('  ', rule)).toBe('This field is required');
      expect(validateField(null, rule)).toBe('This field is required');
      expect(validateField(undefined, rule)).toBe('This field is required');
      expect(validateField('value', rule)).toBeNull();
    });

    it('validates minimum length', () => {
      const rule = { minLength: 5 };
      
      expect(validateField('abc', rule)).toBe('Must be at least 5 characters long');
      expect(validateField('abcde', rule)).toBeNull();
      expect(validateField('abcdef', rule)).toBeNull();
    });

    it('validates maximum length', () => {
      const rule = { maxLength: 5 };
      
      expect(validateField('abcdef', rule)).toBe('Must be no more than 5 characters long');
      expect(validateField('abcde', rule)).toBeNull();
      expect(validateField('abc', rule)).toBeNull();
    });

    it('validates patterns', () => {
      const rule = { pattern: /^[a-z]+$/ };
      
      expect(validateField('ABC', rule)).toBe('Invalid format');
      expect(validateField('123', rule)).toBe('Invalid format');
      expect(validateField('abc', rule)).toBeNull();
    });

    it('validates custom rules', () => {
      const rule = { 
        custom: (value: string) => value === 'forbidden' ? 'This value is not allowed' : null 
      };
      
      expect(validateField('forbidden', rule)).toBe('This value is not allowed');
      expect(validateField('allowed', rule)).toBeNull();
    });

    it('skips validation for empty non-required fields', () => {
      const rule = { minLength: 5, pattern: /^[a-z]+$/ };
      
      expect(validateField('', rule)).toBeNull();
      expect(validateField(null, rule)).toBeNull();
    });
  });

  describe('validateForm', () => {
    it('validates multiple fields', () => {
      const data = {
        name: '',
        email: 'invalid-email',
        phone: '123',
      };
      
      const schema = {
        name: { required: true },
        email: { required: true, pattern: ValidationPatterns.email },
        phone: { required: true, minLength: 10 },
      };
      
      const errors = validateForm(data, schema);
      
      expect(errors.name).toBe('This field is required');
      expect(errors.email).toBe('Invalid format');
      expect(errors.phone).toBe('Must be at least 10 characters long');
    });

    it('returns empty object for valid data', () => {
      const data = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '1234567890',
      };
      
      const schema = {
        name: { required: true },
        email: { required: true, pattern: ValidationPatterns.email },
        phone: { required: true, minLength: 10 },
      };
      
      const errors = validateForm(data, schema);
      
      expect(errors).toEqual({});
    });
  });

  describe('hasValidationErrors', () => {
    it('returns true when errors exist', () => {
      const errors = { name: 'Required', email: 'Invalid' };
      expect(hasValidationErrors(errors)).toBe(true);
    });

    it('returns false when no errors exist', () => {
      const errors = {};
      expect(hasValidationErrors(errors)).toBe(false);
    });
  });

  describe('ValidationPatterns', () => {
    it('validates email pattern', () => {
      expect(ValidationPatterns.email.test('<EMAIL>')).toBe(true);
      expect(ValidationPatterns.email.test('invalid-email')).toBe(false);
      expect(ValidationPatterns.email.test('test@')).toBe(false);
    });

    it('validates phone pattern', () => {
      expect(ValidationPatterns.phone.test('1234567890')).toBe(true);
      expect(ValidationPatterns.phone.test('+1234567890')).toBe(true);
      expect(ValidationPatterns.phone.test('abc')).toBe(false);
    });

    it('validates name pattern', () => {
      expect(ValidationPatterns.name.test('John Doe')).toBe(true);
      expect(ValidationPatterns.name.test("O'Connor")).toBe(true);
      expect(ValidationPatterns.name.test('Jean-Pierre')).toBe(true);
      expect(ValidationPatterns.name.test('123')).toBe(false);
    });
  });

  describe('CommonValidationRules', () => {
    it('validates email with custom message', () => {
      const result = CommonValidationRules.email.custom?.('invalid-email');
      expect(result).toBe('Please enter a valid email address');
    });

    it('validates phone with custom message', () => {
      const result = CommonValidationRules.phone.custom?.('abc');
      expect(result).toBe('Please enter a valid phone number');
    });

    it('validates password with custom message', () => {
      const result = CommonValidationRules.password.custom?.('weak');
      expect(result).toBe('Password must contain at least 8 characters with uppercase, lowercase, and number');
    });
  });
});
