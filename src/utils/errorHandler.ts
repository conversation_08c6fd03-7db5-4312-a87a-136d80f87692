import { 
  App<PERSON>rror, 
  <PERSON>rror<PERSON>ategory, 
  <PERSON>rror<PERSON>ever<PERSON>, 
  ErrorContext, 
  ERROR_MESSAGES,
  RetryConfig,
  DEFAULT_RETRY_CONFIG,
  RecoveryStrategy,
  RecoveryAction
} from '../types/error';

// Generate unique error ID
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Create error context
function createErrorContext(additionalData?: Record<string, any>): ErrorContext {
  return {
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined,
    additionalData
  };
}

// Categorize error based on error message and type
function categorizeError(error: Error): ErrorCategory {
  const message = error.message.toLowerCase();
  
  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return ErrorCategory.NETWORK;
  }
  if (message.includes('auth') || message.includes('unauthorized') || message.includes('token')) {
    return ErrorCategory.AUTHENTICATION;
  }
  if (message.includes('permission') || message.includes('forbidden')) {
    return ErrorCategory.AUTHORIZATION;
  }
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return ErrorCategory.VALIDATION;
  }
  if (message.includes('database') || message.includes('sql') || message.includes('constraint')) {
    return ErrorCategory.DATABASE;
  }
  
  return ErrorCategory.SYSTEM;
}

// Determine error severity
function determineSeverity(category: ErrorCategory, error: Error): ErrorSeverity {
  switch (category) {
    case ErrorCategory.AUTHENTICATION:
    case ErrorCategory.AUTHORIZATION:
      return ErrorSeverity.HIGH;
    case ErrorCategory.DATABASE:
    case ErrorCategory.SYSTEM:
      return ErrorSeverity.MEDIUM;
    case ErrorCategory.VALIDATION:
    case ErrorCategory.USER_INPUT:
      return ErrorSeverity.LOW;
    case ErrorCategory.NETWORK:
      return ErrorSeverity.MEDIUM;
    default:
      return ErrorSeverity.MEDIUM;
  }
}

// Get user-friendly error message
function getUserMessage(category: ErrorCategory, error: Error): string {
  const messages = ERROR_MESSAGES[category];
  const errorMessage = error.message.toLowerCase();
  
  // Try to match specific error types
  for (const [key, message] of Object.entries(messages)) {
    if (key !== 'default' && errorMessage.includes(key)) {
      return message;
    }
  }
  
  return messages.default;
}

// Check if error is retryable
function isRetryable(category: ErrorCategory, retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG): boolean {
  return retryConfig.retryableErrors.includes(category);
}

// Check if error is recoverable
function isRecoverable(category: ErrorCategory): boolean {
  return ![ErrorCategory.AUTHENTICATION, ErrorCategory.AUTHORIZATION].includes(category);
}

// Create standardized AppError
export function createAppError(
  error: Error,
  context?: Partial<ErrorContext>
): AppError {
  const category = categorizeError(error);
  const severity = determineSeverity(category, error);
  const userMessage = getUserMessage(category, error);
  
  return {
    id: generateErrorId(),
    code: `${category.toUpperCase()}_ERROR`,
    message: error.message,
    userMessage,
    category,
    severity,
    context: { ...createErrorContext(), ...context },
    originalError: error,
    retryable: isRetryable(category),
    recoverable: isRecoverable(category)
  };
}

// Enhanced error logger with privacy protection
export function logError(appError: AppError): void {
  const logData = {
    id: appError.id,
    code: appError.code,
    category: appError.category,
    severity: appError.severity,
    timestamp: appError.context.timestamp,
    component: appError.context.component,
    action: appError.context.action,
    url: appError.context.url,
    // Exclude sensitive data like userId from logs in production
    ...(process.env.NODE_ENV === 'development' && {
      message: appError.message,
      userAgent: appError.context.userAgent,
      additionalData: appError.context.additionalData
    })
  };
  
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Application Error:', logData, appError.originalError);
  }
  
  // In production, send to error tracking service
  // Example: Sentry, LogRocket, etc.
  // errorTrackingService.captureError(logData);
}

// Exponential backoff delay calculation
export function calculateRetryDelay(
  retryCount: number, 
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): number {
  const delay = Math.min(
    config.baseDelay * Math.pow(config.backoffMultiplier, retryCount),
    config.maxDelay
  );
  
  // Add jitter to prevent thundering herd
  return delay + Math.random() * 1000;
}

// Create recovery actions based on error
export function createRecoveryActions(appError: AppError): RecoveryAction[] {
  const actions: RecoveryAction[] = [];
  
  if (appError.retryable) {
    actions.push({
      strategy: RecoveryStrategy.RETRY,
      label: 'Try Again',
      action: () => {
        // This will be implemented by the calling component
        console.log('Retry action triggered');
      }
    });
  }
  
  if (appError.category === ErrorCategory.AUTHENTICATION) {
    actions.push({
      strategy: RecoveryStrategy.USER_ACTION_REQUIRED,
      label: 'Sign In',
      action: () => {
        // Redirect to login
        window.location.href = '/login';
      }
    });
  }
  
  if (appError.severity === ErrorSeverity.CRITICAL) {
    actions.push({
      strategy: RecoveryStrategy.RELOAD_REQUIRED,
      label: 'Reload Page',
      action: () => {
        window.location.reload();
      }
    });
  }
  
  return actions;
}

// Graceful degradation helper
export function createFallbackData<T>(
  defaultData: T,
  lastKnownGood?: T
): T {
  return lastKnownGood || defaultData;
}
