import React, { Suspense, useCallback } from "react";
import { Routes, Route, Link, useNavigate } from "react-router-dom";
import { Users, Star, Archive, LogOut, User, Settings } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { DashboardHome } from "../components/dashboard";

// Lazy load dashboard components
const CandidateList = React.lazy(() =>
  import("../components/candidate").then((module) => ({
    default: module.CandidateList,
  }))
);
const CandidateDetails = React.lazy(() =>
  import("../components/candidate").then((module) => ({
    default: module.CandidateDetails,
  }))
);
const AdminProfile = React.lazy(() =>
  import("../components").then((module) => ({
    default: module.AdminProfile,
  }))
);

// Loading component for dashboard routes
function DashboardLoading() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-champagne mx-auto mb-2"></div>
        <p className="text-gray-600 text-sm">Loading...</p>
      </div>
    </div>
  );
}

export function Dashboard() {
  const { signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = useCallback(async () => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  }, [signOut, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200">
        <div className="p-6">
          <h1 className="text-xl font-bold text-dark">Skin Spa New York</h1>
        </div>

        <nav className="mt-6">
          <Link
            to="/dashboard"
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50">
            <Users className="w-5 h-5 mr-3 text-champagne" />
            Dashboard
          </Link>

          <Link
            to="/dashboard/candidates"
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50">
            <Star className="w-5 h-5 mr-3 text-champagne" />
            Candidates
          </Link>

          <Link
            to="/dashboard/archived"
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50">
            <Archive className="w-5 h-5 mr-3 text-champagne" />
            Archived
          </Link>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1">
        <header className="bg-white border-b border-gray-200">
          <div className="px-6 py-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-dark">Dashboard</h2>

            <div className="flex items-center space-x-4">
              <Link
                to="/dashboard/profile"
                className="p-2 text-gray-600 hover:text-dark">
                <User className="w-5 h-5" />
              </Link>

              <Link
                to="/dashboard/settings"
                className="p-2 text-gray-600 hover:text-dark">
                <Settings className="w-5 h-5" />
              </Link>

              <button
                onClick={handleSignOut}
                className="p-2 text-gray-600 hover:text-dark">
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        <div className="p-6">
          <Suspense fallback={<DashboardLoading />}>
            <Routes>
              <Route
                index
                element={<DashboardHome />}
              />
              <Route
                path="candidates"
                element={<CandidateList />}
              />
              <Route
                path="candidates/:id"
                element={<CandidateDetails />}
              />
              <Route
                path="archived"
                element={<CandidateList archived />}
              />
              <Route
                path="profile"
                element={<AdminProfile />}
              />
            </Routes>
          </Suspense>
        </div>
      </main>
    </div>
  );
}
