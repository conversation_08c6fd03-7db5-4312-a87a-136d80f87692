import type { Candidate, AdminProfile, AdminPreferences, Position, Location } from '../../types';

export const mockCandidates: Candidate[] = [
  {
    id: '1',
    full_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    position_id: '1',
    location_id: '1',
    resume_url: 'https://example.com/resume.pdf',
    availability: ['Monday', 'Tuesday', 'Wednesday'],
    rating: 4,
    status: 'new',
    is_new: true,
    created_at: '2024-01-01T00:00:00Z',
    position: { title: 'Massage Therapist' },
    location: { name: 'Downtown Spa' },
  },
  {
    id: '2',
    full_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    position_id: '2',
    location_id: '2',
    resume_url: null,
    availability: ['Thursday', 'Friday'],
    rating: 5,
    status: 'hired',
    is_new: false,
    created_at: '2024-01-02T00:00:00Z',
    position: { title: 'Esthetician' },
    location: { name: 'Uptown Spa' },
  },
];

export const mockPositions: Position[] = [
  {
    id: '1',
    title: 'Massage Therapist',
    description: 'Licensed massage therapist position',
    is_active: true,
  },
  {
    id: '2',
    title: 'Esthetician',
    description: 'Skincare specialist position',
    is_active: true,
  },
];

export const mockLocations: Location[] = [
  {
    id: '1',
    name: 'Downtown Spa',
    address: '123 Main St, Downtown',
  },
  {
    id: '2',
    name: 'Uptown Spa',
    address: '456 Oak Ave, Uptown',
  },
];

export const mockAdminProfile: AdminProfile = {
  id: 'admin-1',
  name: 'Admin User',
  title: 'Spa Manager',
  email: '<EMAIL>',
  phone: '+**********',
};

export const mockAdminPreferences: AdminPreferences = {
  id: 'pref-1',
  admin_id: 'admin-1',
  email_notifications: true,
  new_applications: true,
  status_updates: false,
  daily_digest: true,
};
