# Testing Guide

This project uses Vitest and React Testing Library for comprehensive testing.

## Test Structure

```
src/
├── test/
│   ├── setup.ts          # Test setup and global mocks
│   ├── utils.tsx         # Custom render function with providers
│   ├── mocks/
│   │   └── data.ts       # Mock data for tests
│   └── README.md         # This file
├── components/
│   └── **/__tests__/     # Component tests
├── hooks/
│   └── __tests__/        # Hook tests
├── stores/
│   └── __tests__/        # Store tests
└── utils/
    └── __tests__/        # Utility function tests
```

## Running Tests

```bash
# Run tests in watch mode
npm run test

# Run tests once
npm run test:run

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

## Test Categories

### Unit Tests
- **Components**: Test individual component behavior, props, and user interactions
- **Hooks**: Test custom hook logic and state management
- **Utils**: Test utility functions and validation logic
- **Stores**: Test Zustand store actions and state updates

### Integration Tests
- **Form Validation**: Test complete form submission flows
- **Error Boundaries**: Test error handling and recovery
- **Store Integration**: Test store interactions with services

## Testing Patterns

### Component Testing
```typescript
import { render, screen, fireEvent } from '../../../test/utils';
import { MyComponent } from '../MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('handles user interactions', () => {
    const onSubmit = vi.fn();
    render(<MyComponent onSubmit={onSubmit} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(onSubmit).toHaveBeenCalled();
  });
});
```

### Hook Testing
```typescript
import { renderHook, act } from '@testing-library/react';
import { useMyHook } from '../useMyHook';

describe('useMyHook', () => {
  it('manages state correctly', () => {
    const { result } = renderHook(() => useMyHook());
    
    act(() => {
      result.current.updateValue('new value');
    });
    
    expect(result.current.value).toBe('new value');
  });
});
```

### Store Testing
```typescript
import { renderHook, act } from '@testing-library/react';
import { useMyStore } from '../myStore';

describe('useMyStore', () => {
  beforeEach(() => {
    // Reset store state
    useMyStore.setState({ /* initial state */ });
  });

  it('updates state correctly', () => {
    const { result } = renderHook(() => useMyStore());
    
    act(() => {
      result.current.updateData('new data');
    });
    
    expect(result.current.data).toBe('new data');
  });
});
```

## Mocking Guidelines

### Service Mocking
Services are mocked in `setup.ts` to prevent actual API calls during tests.

### Component Mocking
Large components can be mocked for focused testing:
```typescript
vi.mock('../LargeComponent', () => ({
  LargeComponent: () => <div>Mocked Component</div>
}));
```

### Router Mocking
React Router is mocked in `setup.ts` with common navigation functions.

## Best Practices

1. **Test Behavior, Not Implementation**: Focus on what the component does, not how it does it
2. **Use Semantic Queries**: Prefer `getByRole`, `getByLabelText` over `getByTestId`
3. **Test User Interactions**: Simulate real user behavior with fireEvent
4. **Mock External Dependencies**: Keep tests isolated and fast
5. **Write Descriptive Test Names**: Make test intent clear
6. **Group Related Tests**: Use `describe` blocks to organize tests
7. **Clean Up**: Reset mocks and state between tests

## Coverage Goals

- **Components**: 90%+ coverage for critical user-facing components
- **Hooks**: 100% coverage for custom hooks
- **Utils**: 100% coverage for utility functions
- **Stores**: 90%+ coverage for store actions and state management

## Debugging Tests

1. Use `screen.debug()` to see rendered HTML
2. Use `--reporter=verbose` for detailed test output
3. Use `--ui` flag for interactive debugging
4. Check console logs for async operation issues
